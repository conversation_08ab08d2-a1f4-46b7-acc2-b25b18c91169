//
//  APIEnvironmentSwitcherView.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import SwiftUI

struct APIEnvironmentSwitcherView: View {
    @StateObject private var apiConfig = APIConfiguration.shared
    @State private var showingCustomURLAlert = false
    @State private var customURL = ""
    @State private var showingConfirmation = false
    @State private var pendingEnvironment: APIEnvironment?
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("Current Configuration")) {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Environment:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Text(apiConfig.currentEnvironment.displayName)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        
                        HStack {
                            Text("Base URL:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        Text(apiConfig.baseURL)
                            .font(.caption)
                            .foregroundColor(.blue)
                            .multilineTextAlignment(.trailing)
                        
                        HStack {
                            Text("Status:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                            Image(systemName: apiConfig.validateConfiguration() ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(apiConfig.validateConfiguration() ? .green : .red)
                            Text(apiConfig.validateConfiguration() ? "Valid" : "Invalid")
                                .font(.caption)
                                .foregroundColor(apiConfig.validateConfiguration() ? .green : .red)
                        }
                    }
                    .padding(.vertical, 4)
                }
                
                Section(header: Text("Available Environments")) {
                    ForEach(APIEnvironment.allCases, id: \.self) { environment in
                        EnvironmentRow(
                            environment: environment,
                            isSelected: apiConfig.currentEnvironment == environment,
                            onSelect: {
                                if environment == .custom {
                                    showCustomURLInput()
                                } else {
                                    selectEnvironment(environment)
                                }
                            }
                        )
                    }
                }
                
                Section(header: Text("Actions")) {
                    Button("Test Connection") {
                        testConnection()
                    }
                    .foregroundColor(.blue)
                    
                    Button("Reset to Default") {
                        resetToDefault()
                    }
                    .foregroundColor(.orange)
                    
                    Button("Show Debug Info") {
                        showDebugInfo()
                    }
                    .foregroundColor(.purple)
                }
            }
            .navigationTitle("API Configuration")
            .navigationBarTitleDisplayMode(.inline)
            .alert("Custom API URL", isPresented: $showingCustomURLAlert) {
                TextField("Enter API URL", text: $customURL)
                    .textInputAutocapitalization(.never)
                    .disableAutocorrection(true)
                
                Button("Cancel", role: .cancel) {
                    customURL = ""
                }
                
                Button("Save") {
                    saveCustomURL()
                }
                .disabled(customURL.isEmpty)
            } message: {
                Text("Enter the custom API base URL (including protocol)")
            }
            .alert("Confirm Environment Change", isPresented: $showingConfirmation) {
                Button("Cancel", role: .cancel) {
                    pendingEnvironment = nil
                }
                
                Button("Change") {
                    if let environment = pendingEnvironment {
                        apiConfig.setEnvironment(environment)
                    }
                    pendingEnvironment = nil
                }
            } message: {
                if let environment = pendingEnvironment {
                    Text("Are you sure you want to switch to \(environment.displayName) environment? This will affect all API calls.")
                } else {
                    Text("Confirm environment change")
                }
            }
        }
    }
    
    private func selectEnvironment(_ environment: APIEnvironment) {
        if environment != apiConfig.currentEnvironment {
            pendingEnvironment = environment
            showingConfirmation = true
        }
    }
    
    private func showCustomURLInput() {
        customURL = apiConfig.customBaseURL
        showingCustomURLAlert = true
    }
    
    private func saveCustomURL() {
        guard !customURL.isEmpty else { return }
        
        apiConfig.setCustomURL(customURL)
        apiConfig.setEnvironment(.custom)
        customURL = ""
    }
    
    private func testConnection() {
        Task {
            do {
                let _ = try await EarnBaseAPIService.shared.healthCheck()
                await MainActor.run {
                    // Show success message
                    print("Connection test successful")
                }
            } catch {
                await MainActor.run {
                    // Show error message
                    print("Connection test failed: \(error)")
                }
            }
        }
    }
    
    private func resetToDefault() {
        apiConfig.resetToDefault()
    }
    
    private func showDebugInfo() {
        let debugInfo = apiConfig.debugInfo
        print("API Configuration Debug Info:")
        for (key, value) in debugInfo {
            print("\(key): \(value)")
        }
    }
}

struct EnvironmentRow: View {
    let environment: APIEnvironment
    let isSelected: Bool
    let onSelect: () -> Void
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(environment.displayName)
                        .font(.body)
                        .foregroundColor(.primary)
                    
                    Text(environment.defaultBaseURL)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Preview
struct APIEnvironmentSwitcherView_Previews: PreviewProvider {
    static var previews: some View {
        APIEnvironmentSwitcherView()
    }
}

// MARK: - Settings Integration
extension APIEnvironmentSwitcherView {
    static func settingsRow() -> some View {
        NavigationLink(destination: APIEnvironmentSwitcherView()) {
            HStack {
                Image(systemName: "globe")
                    .foregroundColor(.blue)
                    .frame(width: 24, height: 24)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("API Environment")
                        .font(.body)
                    
                    Text(APIConfiguration.shared.currentEnvironment.displayName)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
    }
}
