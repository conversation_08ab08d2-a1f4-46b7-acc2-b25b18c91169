# Be Vietnam Pro Implementation Guide

## Quick Start Implementation

### 1. Download Required Font Files
Download these specific weights from Google Fonts:
```
BeVietnamPro-Regular.ttf (400)
BeVietnamPro-Medium.ttf (500) 
BeVietnamPro-SemiBold.ttf (600)
BeVietnamPro-Bold.ttf (700)
```

### 2. Add Fonts to iOS Project
1. Create `Fonts` folder in Xcode project
2. Drag .ttf files into the folder
3. Ensure "Add to target" is checked for main app target
4. Verify files appear in Bundle Resources

### 3. Update Info.plist
Add this to your Info.plist:
```xml
<key>UIAppFonts</key>
<array>
    <string>BeVietnamPro-Regular.ttf</string>
    <string>BeVietnamPro-Medium.ttf</string>
    <string>BeVietnamPro-SemiBold.ttf</string>
    <string>BeVietnamPro-Bold.ttf</string>
</array>
```

### 4. Update AppConstants.swift Typography
Replace existing Typography struct with:

```swift
struct Typography {
    // MARK: - Display Fonts
    static let largeTitle = Font.custom("BeVietnamPro-Bold", size: 34, relativeTo: .largeTitle)
    static let title = Font.custom("BeVietnamPro-SemiBold", size: 28, relativeTo: .title)
    static let title2 = Font.custom("BeVietnamPro-SemiBold", size: 22, relativeTo: .title2)
    static let title3 = Font.custom("BeVietnamPro-Medium", size: 20, relativeTo: .title3)
    
    // MARK: - Body Fonts
    static let headline = Font.custom("BeVietnamPro-SemiBold", size: 17, relativeTo: .headline)
    static let body = Font.custom("BeVietnamPro-Regular", size: 17, relativeTo: .body)
    static let callout = Font.custom("BeVietnamPro-Regular", size: 16, relativeTo: .callout)
    static let subheadline = Font.custom("BeVietnamPro-Regular", size: 15, relativeTo: .subheadline)
    
    // MARK: - Small Fonts
    static let footnote = Font.custom("BeVietnamPro-Regular", size: 13, relativeTo: .footnote)
    static let caption = Font.custom("BeVietnamPro-Regular", size: 12, relativeTo: .caption)
    static let caption2 = Font.custom("BeVietnamPro-Regular", size: 11, relativeTo: .caption2)
    
    // MARK: - Button Fonts
    static let buttonLarge = Font.custom("BeVietnamPro-SemiBold", size: 17, relativeTo: .headline)
    static let buttonMedium = Font.custom("BeVietnamPro-Medium", size: 15, relativeTo: .subheadline)
    static let buttonSmall = Font.custom("BeVietnamPro-Medium", size: 13, relativeTo: .footnote)
}
```

## Font Loading Verification

### Debug Font Loading
Add this to your AppDelegate or main app file to verify fonts load correctly:

```swift
func printAvailableFonts() {
    #if DEBUG
    print("=== Available Font Families ===")
    for family in UIFont.familyNames.sorted() {
        print("Family: \(family)")
        for name in UIFont.fontNames(forFamilyName: family) {
            print("  - \(name)")
        }
    }
    
    // Test Be Vietnam Pro specifically
    let testFonts = [
        "BeVietnamPro-Regular",
        "BeVietnamPro-Medium", 
        "BeVietnamPro-SemiBold",
        "BeVietnamPro-Bold"
    ]
    
    print("\n=== Be Vietnam Pro Font Test ===")
    for fontName in testFonts {
        if let font = UIFont(name: fontName, size: 16) {
            print("✅ \(fontName) loaded successfully")
        } else {
            print("❌ \(fontName) failed to load")
        }
    }
    #endif
}
```

## Common Implementation Patterns

### Navigation Titles
```swift
// Large navigation title
.navigationTitle("Trang chủ")
.navigationBarTitleDisplayMode(.large)
.font(AppConstants.Typography.title)

// Inline navigation title  
.navigationTitle("Chi tiết lớp học")
.navigationBarTitleDisplayMode(.inline)
.font(AppConstants.Typography.headline)
```

### Section Headers
```swift
Text("Thao tác nhanh")
    .font(AppConstants.Typography.headline)
    .foregroundColor(AppConstants.Colors.textPrimary)
    .padding(.bottom, 8)
```

### Card Content
```swift
VStack(alignment: .leading, spacing: 8) {
    // Card title
    Text("Lớp học hôm nay")
        .font(AppConstants.Typography.headline)
        .foregroundColor(AppConstants.Colors.textPrimary)
    
    // Card description
    Text("Không có lớp học nào được lên lịch")
        .font(AppConstants.Typography.body)
        .foregroundColor(AppConstants.Colors.textSecondary)
        .multilineTextAlignment(.leading)
}
```

### Button Styles
```swift
// Primary button
Button("Xem tất cả") {
    // Action
}
.font(AppConstants.Typography.buttonMedium)
.foregroundColor(.white)
.padding(.horizontal, 16)
.padding(.vertical, 8)
.background(AppConstants.Colors.primary)
.cornerRadius(8)

// Secondary button
Button("Hủy") {
    // Action  
}
.font(AppConstants.Typography.buttonMedium)
.foregroundColor(AppConstants.Colors.primary)
.padding(.horizontal, 16)
.padding(.vertical, 8)
.background(AppConstants.Colors.surface)
.cornerRadius(8)
```

### Form Labels and Inputs
```swift
VStack(alignment: .leading, spacing: 4) {
    // Label
    Text("Tên lớp học")
        .font(AppConstants.Typography.callout)
        .foregroundColor(AppConstants.Colors.textPrimary)
    
    // Input field
    TextField("Nhập tên lớp học", text: $className)
        .font(AppConstants.Typography.body)
        .padding(12)
        .background(AppConstants.Colors.surface)
        .cornerRadius(8)
}
```

## Vietnamese Text Optimization

### Diacritics Handling
Be Vietnam Pro handles Vietnamese diacritics beautifully. Test with these common phrases:

```swift
let vietnameseTestText = [
    "Chào mừng đến với ứng dụng",
    "Giảng viên Nguyễn Văn A", 
    "Lớp học hôm nay",
    "Điểm danh học sinh",
    "Bài tập về nhà",
    "Thông báo quan trọng"
]
```

### Line Height for Vietnamese
Vietnamese text with diacritics needs slightly more line height:

```swift
Text("Văn bản tiếng Việt với dấu")
    .font(AppConstants.Typography.body)
    .lineSpacing(2) // Add 2pt line spacing for Vietnamese
    .multilineTextAlignment(.leading)
```

## Performance Considerations

### Font Caching
SwiftUI automatically caches custom fonts, but you can optimize loading:

```swift
// Pre-load fonts in AppDelegate
func preloadFonts() {
    let fontNames = [
        "BeVietnamPro-Regular",
        "BeVietnamPro-Medium",
        "BeVietnamPro-SemiBold", 
        "BeVietnamPro-Bold"
    ]
    
    for fontName in fontNames {
        _ = UIFont(name: fontName, size: 16)
    }
}
```

### Memory Usage
- Only include weights you actually use
- Be Vietnam Pro is optimized for file size
- Each weight adds ~50-100KB to app bundle

## Troubleshooting

### Font Not Loading
1. **Check file names**: Must match exactly (case-sensitive)
2. **Verify Info.plist**: Font file names must be exact
3. **Check target membership**: Fonts must be added to app target
4. **Clean build**: Product → Clean Build Folder

### Common Font Name Issues
```swift
// ❌ Wrong - these won't work
Font.custom("Be Vietnam Pro", size: 16)
Font.custom("BeVietnamPro", size: 16)
Font.custom("be-vietnam-pro-regular", size: 16)

// ✅ Correct - exact PostScript names
Font.custom("BeVietnamPro-Regular", size: 16)
Font.custom("BeVietnamPro-SemiBold", size: 16)
```

### Testing Font Names
Use this code to find exact font names:
```swift
if let fontURL = Bundle.main.url(forResource: "BeVietnamPro-Regular", withExtension: "ttf"),
   let fontData = NSData(contentsOf: fontURL),
   let provider = CGDataProvider(data: fontData),
   let font = CGFont(provider) {
    print("Font PostScript name: \(font.postScriptName ?? "Unknown")")
}
```

## Migration Checklist

- [ ] Download Be Vietnam Pro fonts (Regular, Medium, SemiBold, Bold)
- [ ] Add font files to Xcode project with target membership
- [ ] Update Info.plist with UIAppFonts array
- [ ] Update AppConstants.Typography with new font definitions
- [ ] Test font loading with debug code
- [ ] Replace all system font usage throughout app
- [ ] Test Vietnamese text rendering
- [ ] Verify Dynamic Type scaling works
- [ ] Test on different device sizes
- [ ] Check accessibility contrast ratios
- [ ] Test in both light and dark modes

## Support Resources

- **Google Fonts**: https://fonts.google.com/specimen/Be+Vietnam+Pro
- **Font License**: Open Font License (free commercial use)
- **Designer**: Nguyen Duc Toan
- **Language Support**: Latin, Vietnamese, Cyrillic
- **File Formats**: TTF, OTF, WOFF, WOFF2
