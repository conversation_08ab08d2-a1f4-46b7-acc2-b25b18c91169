# Font Guidelines for Mobile App Template

## Overview
This document defines the typography system using **Be Vietnam Pro** from Google Fonts. Be Vietnam Pro is a Neo Grotesk typeface that is well-suited for tech companies and startups, with refined Vietnamese letterforms and adaptive diacritics.

## Primary Font Family

### Be Vietnam Pro
- **Source**: Google Fonts
- **Type**: Neo Grotesk Sans-serif
- **Language Support**: Latin, Vietnamese, Cyrillic
- **License**: Open Font License (free for commercial use)
- **Designer**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>

## Available Weights and Styles

### Regular Weights
- **Thin**: 100
- **Extra Light**: 200  
- **Light**: 300
- **Regular**: 400 (Default)
- **Medium**: 500
- **Semi Bold**: 600
- **Bold**: 700
- **Extra Bold**: 800
- **Black**: 900

### Italic Styles
- All weights above are available in italic variants
- Use sparingly for emphasis or quotes

## Typography Scale

### Display Text (Màn hình lớn)
```swift
// Large Title - App headers, splash screens
static let largeTitle = Font.custom("BeVietnamPro-Bold", size: 34)
    .weight(.bold)

// Title 1 - Main page titles
static let title = Font.custom("BeVietnamPro-SemiBold", size: 28)
    .weight(.semibold)

// Title 2 - Section headers
static let title2 = Font.custom("BeVietnamPro-SemiBold", size: 22)
    .weight(.semibold)

// Title 3 - Subsection headers
static let title3 = Font.custom("BeVietnamPro-Medium", size: 20)
    .weight(.medium)
```

### Body Text (Nội dung chính)
```swift
// Headline - Important content, card titles
static let headline = Font.custom("BeVietnamPro-SemiBold", size: 17)
    .weight(.semibold)

// Body - Main content, descriptions
static let body = Font.custom("BeVietnamPro-Regular", size: 17)
    .weight(.regular)

// Callout - Secondary content
static let callout = Font.custom("BeVietnamPro-Regular", size: 16)
    .weight(.regular)

// Subheadline - Supporting text
static let subheadline = Font.custom("BeVietnamPro-Regular", size: 15)
    .weight(.regular)
```

### Small Text (Văn bản nhỏ)
```swift
// Footnote - Captions, metadata
static let footnote = Font.custom("BeVietnamPro-Regular", size: 13)
    .weight(.regular)

// Caption 1 - Labels, small descriptions
static let caption = Font.custom("BeVietnamPro-Regular", size: 12)
    .weight(.regular)

// Caption 2 - Very small text, timestamps
static let caption2 = Font.custom("BeVietnamPro-Regular", size: 11)
    .weight(.regular)
```

## Font Installation for iOS

### Step 1: Download Font Files
Download the following weights from Google Fonts:
- BeVietnamPro-Regular.ttf (400)
- BeVietnamPro-Medium.ttf (500)
- BeVietnamPro-SemiBold.ttf (600)
- BeVietnamPro-Bold.ttf (700)

### Step 2: Add to Xcode Project
1. Drag font files into Xcode project
2. Ensure "Add to target" is checked
3. Add fonts to Info.plist:

```xml
<key>UIAppFonts</key>
<array>
    <string>BeVietnamPro-Regular.ttf</string>
    <string>BeVietnamPro-Medium.ttf</string>
    <string>BeVietnamPro-SemiBold.ttf</string>
    <string>BeVietnamPro-Bold.ttf</string>
</array>
```

### Step 3: Update AppConstants.swift
```swift
struct Typography {
    // Display Fonts
    static let largeTitle = Font.custom("BeVietnamPro-Bold", size: 34)
    static let title = Font.custom("BeVietnamPro-SemiBold", size: 28)
    static let title2 = Font.custom("BeVietnamPro-SemiBold", size: 22)
    static let title3 = Font.custom("BeVietnamPro-Medium", size: 20)
    
    // Body Fonts
    static let headline = Font.custom("BeVietnamPro-SemiBold", size: 17)
    static let body = Font.custom("BeVietnamPro-Regular", size: 17)
    static let callout = Font.custom("BeVietnamPro-Regular", size: 16)
    static let subheadline = Font.custom("BeVietnamPro-Regular", size: 15)
    
    // Small Fonts
    static let footnote = Font.custom("BeVietnamPro-Regular", size: 13)
    static let caption = Font.custom("BeVietnamPro-Regular", size: 12)
    static let caption2 = Font.custom("BeVietnamPro-Regular", size: 11)
}
```

## Usage Guidelines

### Primary Usage Rules
1. **Use Be Vietnam Pro for all text** - Replace system fonts entirely
2. **Maintain consistent hierarchy** - Follow the defined typography scale
3. **Limit weight variations** - Use Regular, Medium, SemiBold, Bold only
4. **Avoid mixing fonts** - Be Vietnam Pro should be the only typeface

### Weight Selection Guide
- **Regular (400)**: Body text, descriptions, normal content
- **Medium (500)**: Subtle emphasis, secondary headers
- **SemiBold (600)**: Section headers, important labels
- **Bold (700)**: Main titles, primary headers, CTAs

### Size Guidelines
- **34pt+**: App titles, splash screens only
- **28pt**: Main page headers
- **22pt**: Section headers
- **17-20pt**: Content headers and body text
- **13-16pt**: Secondary content
- **11-12pt**: Captions and metadata

## Accessibility Considerations

### Contrast Requirements
- **Large text (18pt+)**: Minimum 3:1 contrast ratio
- **Normal text (<18pt)**: Minimum 4.5:1 contrast ratio
- **Bold text**: Can use 3:1 ratio at smaller sizes

### Dynamic Type Support
```swift
// Support iOS Dynamic Type
static let body = Font.custom("BeVietnamPro-Regular", size: 17, relativeTo: .body)
static let headline = Font.custom("BeVietnamPro-SemiBold", size: 17, relativeTo: .headline)
```

### Readability Guidelines
- **Line height**: 1.2-1.4x font size for optimal readability
- **Letter spacing**: Default (0) for most cases, slight increase for all-caps
- **Paragraph spacing**: 0.5-1x line height between paragraphs

## Implementation Examples

### Navigation Bar
```swift
.navigationTitle("Trang chủ")
.font(AppConstants.Typography.title2)
```

### Card Title
```swift
Text("Lớp học hôm nay")
    .font(AppConstants.Typography.headline)
    .foregroundColor(AppConstants.Colors.textPrimary)
```

### Body Content
```swift
Text("Không có lớp học nào được lên lịch cho hôm nay")
    .font(AppConstants.Typography.body)
    .foregroundColor(AppConstants.Colors.textSecondary)
```

### Button Text
```swift
Text("Xem tất cả")
    .font(AppConstants.Typography.callout)
    .fontWeight(.semibold)
```

## Testing Checklist

Before implementing font changes:
- [ ] All font files are properly added to Xcode project
- [ ] Info.plist includes all font file names
- [ ] Font names match exactly (case-sensitive)
- [ ] Test on different device sizes
- [ ] Verify Vietnamese diacritics render correctly
- [ ] Check Dynamic Type scaling
- [ ] Test in both light and dark modes
- [ ] Validate accessibility contrast ratios

## Migration from System Fonts

### Step-by-Step Migration
1. **Install Be Vietnam Pro fonts** (see installation steps above)
2. **Update AppConstants.Typography** with new font definitions
3. **Replace system font usage** throughout the app
4. **Test thoroughly** on different devices and text sizes
5. **Verify Vietnamese text** renders correctly

### Common Font Name Issues
- Use exact PostScript names: "BeVietnamPro-Regular", "BeVietnamPro-SemiBold"
- Check font names in Font Book app on macOS
- Test font loading with: `UIFont.familyNames` in debug

## Notes
- Be Vietnam Pro provides excellent readability for Vietnamese text
- The Neo Grotesk style gives a modern, tech-friendly appearance
- Consistent with current design trends for mobile apps
- Free for commercial use under Open Font License
- Regular updates and maintenance by the font creators
