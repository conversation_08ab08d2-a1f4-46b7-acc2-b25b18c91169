{"augment.advanced": {"mcpServers": [{"name": "odoo", "command": "docker", "args": ["run", "-i", "--rm", "-e", "ODOO_URL", "-e", "ODOO_DB", "-e", "ODOO_USERNAME", "-e", "ODOO_PASSWORD", "ghcr.io/tuanle96/mcp-odoo:latest"], "env": {"ODOO_URL": "https://lms-dev.earnbase.io", "ODOO_DB": "lms_uat", "ODOO_USERNAME": "admin", "ODOO_PASSWORD": "ebill@2025"}}, {"name": "postgres", "command": "docker", "args": ["run", "-i", "--rm", "-e", "DATABASE_URI", "crystaldba/postgres-mcp", "--access-mode=unrestricted"], "env": {"DATABASE_URI": "postgresql://lms:lms@localhost:38432/lms_uat"}}]}}