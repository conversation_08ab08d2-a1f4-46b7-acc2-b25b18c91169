//
//  AuthDebugUtils.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - Authentication Debug Utilities
class AuthDebugUtils {
    static let shared = AuthDebugUtils()
    
    private init() {}
    
    // MARK: - Force Clear All Authentication Data
    
    /// Completely clears ALL authentication data from the app
    /// Use this when you want to ensure a completely clean state
    func forceCompleteLogout() {
        print("🧹 AuthDebugUtils: Starting COMPLETE authentication data cleanup")
        
        // 1. Clear InstructorAuthService
        Task {
            await InstructorAuthService.shared.logout()
        }
        
        // 2. Clear AuthStateManager
        Task {
            await AuthStateManager().logout()
        }
        
        // 3. Clear TokenManager
        TokenManager.shared.clearToken()
        
        // 4. Clear all UserDefaults auth data
        clearAllUserDefaults()
        
        // 5. Clear all Keychain auth data
        clearAllKeychain()
        
        // 6. Clear DataCleanupManager
        DataCleanupManager.shared.clearAllUserData()
        
        print("🧹 AuthDebugUtils: COMPLETE authentication data cleanup finished")
    }
    
    // MARK: - Clear UserDefaults
    
    private func clearAllUserDefaults() {
        let authKeys = [
            "linkx_access_token",
            "linkx_current_user",
            "saved_email",
            "biometric_enabled",
            "access_token",
            "refresh_token",
            "user_id",
            "current_user",
            "instructor_token",
            "instructor_user"
        ]
        
        for key in authKeys {
            UserDefaults.standard.removeObject(forKey: key)
            print("🧹 Cleared UserDefaults key: \(key)")
        }
        
        UserDefaults.standard.synchronize()
    }
    
    // MARK: - Clear Keychain
    
    private func clearAllKeychain() {
        let keychainManager = KeychainManager.shared
        
        let authKeys = [
            "access_token",
            "refresh_token",
            "saved_credentials",
            "linkx_access_token",
            "linkx_refresh_token",
            "instructor_token",
            "instructor_refresh_token",
            "user_private_key",
            "biometric_data"
        ]
        
        for key in authKeys {
            try? keychainManager.delete(key: key)
            print("🧹 Cleared Keychain key: \(key)")
        }
        
        // Also clear using AppConstants keys
        try? keychainManager.deleteAccessToken()
        try? keychainManager.deleteRefreshToken()
    }
    
    // MARK: - Debug Information
    
    /// Logs current authentication state for debugging
    func logCurrentAuthState() {
        print("🔍 === CURRENT AUTHENTICATION STATE ===")
        
        // InstructorAuthService state
        print("🔍 InstructorAuthService:")
        print("   - isAuthenticated: \(InstructorAuthService.shared.isAuthenticated)")
        print("   - currentInstructor: \(InstructorAuthService.shared.currentInstructor?.displayName ?? "nil")")
        print("   - isLoading: \(InstructorAuthService.shared.isLoading)")
        
        // TokenManager state
        print("🔍 TokenManager:")
        print("   - hasToken: \(TokenManager.shared.getToken() != nil)")
        print("   - isLoggedIn: \(TokenManager.shared.isLoggedIn)")
        if let token = TokenManager.shared.getToken() {
            print("   - token preview: \(String(token.prefix(20)))...")
        }
        
        // UserDefaults state
        print("🔍 UserDefaults:")
        print("   - linkx_access_token: \(UserDefaults.standard.string(forKey: "linkx_access_token") != nil)")
        print("   - linkx_current_user: \(UserDefaults.standard.data(forKey: "linkx_current_user") != nil)")
        print("   - saved_email: \(UserDefaults.standard.string(forKey: "saved_email") ?? "nil")")
        
        // Keychain state
        print("🔍 Keychain:")
        let hasKeychainToken = (try? KeychainManager.shared.loadAccessToken()) != nil
        print("   - access_token: \(hasKeychainToken)")
        
        let hasRefreshToken = (try? KeychainManager.shared.loadRefreshToken()) != nil
        print("   - refresh_token: \(hasRefreshToken)")
        
        let hasSavedCredentials = (try? KeychainManager.shared.loadString(key: "saved_credentials")) != nil
        print("   - saved_credentials: \(hasSavedCredentials)")
        
        print("🔍 =====================================")
    }
    
    // MARK: - Verification
    
    /// Verifies that all authentication data has been cleared
    func verifyCompleteLogout() -> Bool {
        // Check InstructorAuthService
        let instructorCleared = !InstructorAuthService.shared.isAuthenticated && 
                               InstructorAuthService.shared.currentInstructor == nil
        
        // Check TokenManager
        let tokenManagerCleared = !TokenManager.shared.isLoggedIn && 
                                 TokenManager.shared.getToken() == nil
        
        // Check UserDefaults
        let userDefaultsCleared = UserDefaults.standard.string(forKey: "linkx_access_token") == nil &&
                                 UserDefaults.standard.data(forKey: "linkx_current_user") == nil &&
                                 UserDefaults.standard.string(forKey: "saved_email") == nil
        
        // Check Keychain
        let keychainCleared = (try? KeychainManager.shared.loadAccessToken()) == nil &&
                             (try? KeychainManager.shared.loadRefreshToken()) == nil
        
        let isCompletelyCleared = instructorCleared && tokenManagerCleared && 
                                 userDefaultsCleared && keychainCleared
        
        print("🔍 Logout Verification:")
        print("   - InstructorAuthService: \(instructorCleared ? "✅" : "❌")")
        print("   - TokenManager: \(tokenManagerCleared ? "✅" : "❌")")
        print("   - UserDefaults: \(userDefaultsCleared ? "✅" : "❌")")
        print("   - Keychain: \(keychainCleared ? "✅" : "❌")")
        print("   - Overall: \(isCompletelyCleared ? "✅ CLEAN" : "❌ NOT CLEAN")")
        
        return isCompletelyCleared
    }
    
    // MARK: - Reset to Fresh State
    
    /// Resets app to completely fresh state (like first install)
    func resetToFreshState() {
        print("🔄 AuthDebugUtils: Resetting to fresh state...")
        
        // Force complete logout
        forceCompleteLogout()
        
        // Wait a bit for async operations
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            // Verify clean state
            let isClean = self.verifyCompleteLogout()
            
            if isClean {
                print("✅ App reset to fresh state successfully")
            } else {
                print("⚠️ App reset may not be complete - check logs")
                self.logCurrentAuthState()
            }
        }
    }
}

// MARK: - Debug View Helper
#if DEBUG
extension AuthDebugUtils {
    /// Creates a debug view for testing authentication state
    static func createDebugView() -> some View {
        return AuthDebugView()
    }
}

import SwiftUI

struct AuthDebugView: View {
    @State private var showingAlert = false
    @State private var alertMessage = ""
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Authentication Debug")
                .font(.title)
                .fontWeight(.bold)
            
            Button("Log Current Auth State") {
                AuthDebugUtils.shared.logCurrentAuthState()
                alertMessage = "Check console for auth state details"
                showingAlert = true
            }
            .buttonStyle(.borderedProminent)
            
            Button("Force Complete Logout") {
                AuthDebugUtils.shared.forceCompleteLogout()
                alertMessage = "Complete logout performed"
                showingAlert = true
            }
            .buttonStyle(.bordered)
            
            Button("Reset to Fresh State") {
                AuthDebugUtils.shared.resetToFreshState()
                alertMessage = "App reset to fresh state"
                showingAlert = true
            }
            .buttonStyle(.bordered)
            
            Button("Verify Logout") {
                let isClean = AuthDebugUtils.shared.verifyCompleteLogout()
                alertMessage = isClean ? "✅ All auth data cleared" : "❌ Some auth data remains"
                showingAlert = true
            }
            .buttonStyle(.bordered)
        }
        .padding()
        .alert("Debug Result", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }
}
#endif
