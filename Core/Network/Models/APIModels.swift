//
//  APIModels.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - Base Response Models
struct ResponseBase: Codable {
    let success: Bool
    let message: String?
    let timestamp: String?
}

struct APIStatus: Codable {
    let status: String
    let version: String
    let timestamp: String
}

// MARK: - Authentication Models
struct UnifiedLoginRequest: Codable {
    let login: String // Can be username or email
    let password: String
    let deviceInfo: DeviceInfo?
    
    struct DeviceInfo: Codable {
        let deviceId: String?
        let deviceName: String?
        let platform: String?
        let appVersion: String?
    }
}

struct EnhancedTokenResponse: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int
    let user: UserInfo
    let deviceId: String?
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
        case user
        case deviceId = "device_id"
    }
}

struct TokenResponse: Codable {
    let accessToken: String
    let refreshToken: String?
    let tokenType: String
    let expiresIn: Int
    
    enum CodingKeys: String, CodingKey {
        case accessToken = "access_token"
        case refreshToken = "refresh_token"
        case tokenType = "token_type"
        case expiresIn = "expires_in"
    }
}

struct UserInfoResponse: Codable {
    let user: UserInfo
    let permissions: [String]?
    let roles: [String]?
}

struct UserInfo: Codable {
    let id: Int
    let username: String?
    let email: String
    let firstName: String?
    let lastName: String?
    let avatar: String?
    let isActive: Bool
    let isVerified: Bool
    let createdAt: String
    let updatedAt: String
    
    enum CodingKeys: String, CodingKey {
        case id, username, email
        case firstName = "first_name"
        case lastName = "last_name"
        case avatar
        case isActive = "is_active"
        case isVerified = "is_verified"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - User Registration Models
struct UserRegistrationRequest: Codable {
    let username: String
    let email: String
    let password: String
    let firstName: String?
    let lastName: String?
    let acceptTerms: Bool
    
    enum CodingKeys: String, CodingKey {
        case username, email, password
        case firstName = "first_name"
        case lastName = "last_name"
        case acceptTerms = "accept_terms"
    }
}

struct UserRegistrationResponse: Codable {
    let user: UserInfo
    let message: String
    let requiresEmailVerification: Bool
    
    enum CodingKeys: String, CodingKey {
        case user, message
        case requiresEmailVerification = "requires_email_verification"
    }
}

// MARK: - Password Management Models
struct ForgotPasswordRequest: Codable {
    let email: String
}

struct ForgotPasswordResponse: Codable {
    let message: String
    let resetTokenSent: Bool
    
    enum CodingKeys: String, CodingKey {
        case message
        case resetTokenSent = "reset_token_sent"
    }
}

struct ResetPasswordRequest: Codable {
    let token: String
    let newPassword: String
    
    enum CodingKeys: String, CodingKey {
        case token
        case newPassword = "new_password"
    }
}

struct ResetPasswordResponse: Codable {
    let message: String
    let success: Bool
}

struct EnhancedPasswordChangeRequest: Codable {
    let currentPassword: String
    let newPassword: String
    let confirmPassword: String
    let logoutOtherDevices: Bool?
    
    enum CodingKeys: String, CodingKey {
        case currentPassword = "current_password"
        case newPassword = "new_password"
        case confirmPassword = "confirm_password"
        case logoutOtherDevices = "logout_other_devices"
    }
}

struct EnhancedPasswordChangeResponse: Codable {
    let message: String
    let success: Bool
    let newTokenRequired: Bool?
    
    enum CodingKeys: String, CodingKey {
        case message, success
        case newTokenRequired = "new_token_required"
    }
}

// MARK: - Email Verification Models
struct EmailVerificationRequest: Codable {
    let token: String
}

struct EmailVerificationResponse: Codable {
    let message: String
    let success: Bool
    let user: UserInfo?
}

// MARK: - Device Registration Models
struct DeviceRegistrationRequest: Codable {
    let deviceId: String
    let deviceName: String
    let platform: String
    let appVersion: String
    let pushToken: String?
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case deviceName = "device_name"
        case platform
        case appVersion = "app_version"
        case pushToken = "push_token"
    }
}

struct DeviceRegistrationResponse: Codable {
    let deviceId: String
    let message: String
    let success: Bool
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case message, success
    }
}

// MARK: - File Management Models
struct FileResponseSchema: Codable {
    let id: String
    let filename: String
    let originalName: String
    let mimeType: String
    let size: Int
    let url: String
    let downloadUrl: String?
    let previewUrl: String?
    let uploadedAt: String
    
    enum CodingKeys: String, CodingKey {
        case id, filename
        case originalName = "original_name"
        case mimeType = "mime_type"
        case size, url
        case downloadUrl = "download_url"
        case previewUrl = "preview_url"
        case uploadedAt = "uploaded_at"
    }
}

// MARK: - Chatbot Models
struct ChatSessionCreate: Codable {
    let websiteCode: String?
    let userAgent: String?
    let referrer: String?
    
    enum CodingKeys: String, CodingKey {
        case websiteCode = "website_code"
        case userAgent = "user_agent"
        case referrer
    }
}

struct ChatSessionResponse: Codable {
    let sessionId: String
    let status: String
    let createdAt: String
    let expiresAt: String?
    
    enum CodingKeys: String, CodingKey {
        case sessionId = "session_id"
        case status
        case createdAt = "created_at"
        case expiresAt = "expires_at"
    }
}

struct ChatMessageCreate: Codable {
    let message: String
    let messageType: String?
    
    enum CodingKeys: String, CodingKey {
        case message
        case messageType = "message_type"
    }
}

struct ChatMessageResponse: Codable {
    let messageId: String
    let message: String
    let response: String?
    let messageType: String
    let timestamp: String
    
    enum CodingKeys: String, CodingKey {
        case messageId = "message_id"
        case message, response
        case messageType = "message_type"
        case timestamp
    }
}

struct ChatMessageListResponse: Codable {
    let messages: [ChatMessageResponse]
    let totalCount: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case messages
        case totalCount = "total_count"
        case hasMore = "has_more"
    }
}

struct ChatFeedbackCreate: Codable {
    let rating: Int // 1-5
    let feedback: String?
    let feedbackType: String?
    
    enum CodingKeys: String, CodingKey {
        case rating, feedback
        case feedbackType = "feedback_type"
    }
}

struct ChatFeedbackResponse: Codable {
    let feedbackId: String
    let message: String
    let success: Bool
    
    enum CodingKeys: String, CodingKey {
        case feedbackId = "feedback_id"
        case message, success
    }
}

struct ChatbotConfigResponse: Codable {
    let enabled: Bool
    let welcomeMessage: String?
    let placeholder: String?
    let theme: ChatbotTheme?
    
    struct ChatbotTheme: Codable {
        let primaryColor: String?
        let backgroundColor: String?
        let textColor: String?
        
        enum CodingKeys: String, CodingKey {
            case primaryColor = "primary_color"
            case backgroundColor = "background_color"
            case textColor = "text_color"
        }
    }
}
