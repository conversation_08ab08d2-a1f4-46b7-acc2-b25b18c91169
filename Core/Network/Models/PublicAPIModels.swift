//
//  PublicAPIModels.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation

// MARK: - Public Events Models
struct PublicEventListResponse: Codable {
    let events: [PublicEvent]
    let totalCount: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case events
        case totalCount = "total_count"
        case hasMore = "has_more"
    }
}

struct PublicEvent: Codable {
    let id: Int
    let title: String
    let description: String?
    let startDate: String
    let endDate: String?
    let location: String?
    let maxParticipants: Int?
    let currentParticipants: Int
    let registrationDeadline: String?
    let isActive: Bool
    let imageUrl: String?
    let eventType: String?
    
    enum CodingKeys: String, CodingKey {
        case id, title, description
        case startDate = "start_date"
        case endDate = "end_date"
        case location
        case maxParticipants = "max_participants"
        case currentParticipants = "current_participants"
        case registrationDeadline = "registration_deadline"
        case isActive = "is_active"
        case imageUrl = "image_url"
        case eventType = "event_type"
    }
}

struct PublicEventDetailResponse: Codable {
    let event: PublicEventDetail
}

struct PublicEventDetail: Codable {
    let id: Int
    let title: String
    let description: String?
    let fullDescription: String?
    let startDate: String
    let endDate: String?
    let location: String?
    let address: String?
    let maxParticipants: Int?
    let currentParticipants: Int
    let registrationDeadline: String?
    let isActive: Bool
    let imageUrl: String?
    let eventType: String?
    let agenda: [EventAgendaItem]?
    let speakers: [EventSpeaker]?
    let requirements: [String]?
    
    enum CodingKeys: String, CodingKey {
        case id, title, description
        case fullDescription = "full_description"
        case startDate = "start_date"
        case endDate = "end_date"
        case location, address
        case maxParticipants = "max_participants"
        case currentParticipants = "current_participants"
        case registrationDeadline = "registration_deadline"
        case isActive = "is_active"
        case imageUrl = "image_url"
        case eventType = "event_type"
        case agenda, speakers, requirements
    }
}

struct EventAgendaItem: Codable {
    let time: String
    let title: String
    let description: String?
    let speaker: String?
}

struct EventSpeaker: Codable {
    let name: String
    let title: String?
    let bio: String?
    let imageUrl: String?
    
    enum CodingKeys: String, CodingKey {
        case name, title, bio
        case imageUrl = "image_url"
    }
}

// MARK: - Event Registration Models
struct PublicEventRegistrationCreate: Codable {
    let firstName: String
    let lastName: String
    let email: String
    let phone: String?
    let organization: String?
    let jobTitle: String?
    let specialRequirements: String?
    let marketingConsent: Bool?
    
    enum CodingKeys: String, CodingKey {
        case firstName = "first_name"
        case lastName = "last_name"
        case email, phone, organization
        case jobTitle = "job_title"
        case specialRequirements = "special_requirements"
        case marketingConsent = "marketing_consent"
    }
}

struct PublicEventRegistrationResponse: Codable {
    let registrationId: String
    let event: PublicEvent
    let participant: EventParticipant
    let status: String
    let registeredAt: String
    let confirmationCode: String?
    
    enum CodingKeys: String, CodingKey {
        case registrationId = "registration_id"
        case event, participant, status
        case registeredAt = "registered_at"
        case confirmationCode = "confirmation_code"
    }
}

struct EventParticipant: Codable {
    let firstName: String
    let lastName: String
    let email: String
    let phone: String?
    let organization: String?
    
    enum CodingKeys: String, CodingKey {
        case firstName = "first_name"
        case lastName = "last_name"
        case email, phone, organization
    }
}

struct PublicEventRegistrationListResponse: Codable {
    let registrations: [PublicEventRegistrationResponse]
    let totalCount: Int
    
    enum CodingKeys: String, CodingKey {
        case registrations
        case totalCount = "total_count"
    }
}

// MARK: - Workshop Types Models
struct PublicWorkshopTypeListResponse: Codable {
    let workshopTypes: [PublicWorkshopType]
    let totalCount: Int
    
    enum CodingKeys: String, CodingKey {
        case workshopTypes = "workshop_types"
        case totalCount = "total_count"
    }
}

struct PublicWorkshopType: Codable {
    let id: Int
    let name: String
    let description: String?
    let duration: String?
    let difficulty: String?
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, duration, difficulty
        case isActive = "is_active"
    }
}

// MARK: - Courses Models
struct PublicCourseListResponse: Codable {
    let courses: [PublicCourse]
    let totalCount: Int
    let hasMore: Bool
    
    enum CodingKeys: String, CodingKey {
        case courses
        case totalCount = "total_count"
        case hasMore = "has_more"
    }
}

struct PublicCourse: Codable {
    let id: Int
    let title: String
    let description: String?
    let shortDescription: String?
    let duration: String?
    let level: String?
    let price: Double?
    let discountPrice: Double?
    let imageUrl: String?
    let category: CourseCategory?
    let instructor: CourseInstructor?
    let rating: Double?
    let totalStudents: Int
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, title, description
        case shortDescription = "short_description"
        case duration, level, price
        case discountPrice = "discount_price"
        case imageUrl = "image_url"
        case category, instructor, rating
        case totalStudents = "total_students"
        case isActive = "is_active"
    }
}

struct CourseCategory: Codable {
    let id: Int
    let name: String
    let slug: String?
}

struct CourseInstructor: Codable {
    let id: Int
    let name: String
    let title: String?
    let bio: String?
    let imageUrl: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, title, bio
        case imageUrl = "image_url"
    }
}

// MARK: - Classes Models
struct PublicClassListResponse: Codable {
    let classes: [PublicClass]
    let totalCount: Int
    let hasMore: Bool
    let pagination: PaginationInfo?
    
    enum CodingKeys: String, CodingKey {
        case classes
        case totalCount = "total_count"
        case hasMore = "has_more"
        case pagination
    }
}

struct PublicClass: Codable {
    let id: Int
    let name: String
    let course: PublicCourse?
    let startDate: String
    let endDate: String?
    let schedule: String?
    let location: ClassLocation?
    let instructor: CourseInstructor?
    let maxStudents: Int?
    let currentStudents: Int
    let learningType: String?
    let status: String
    let registrationDeadline: String?
    
    enum CodingKeys: String, CodingKey {
        case id, name, course
        case startDate = "start_date"
        case endDate = "end_date"
        case schedule, location, instructor
        case maxStudents = "max_students"
        case currentStudents = "current_students"
        case learningType = "learning_type"
        case status
        case registrationDeadline = "registration_deadline"
    }
}

struct PublicClassDetailResponse: Codable {
    let classDetail: PublicClassDetail
    
    enum CodingKeys: String, CodingKey {
        case classDetail = "class"
    }
}

struct PublicClassDetail: Codable {
    let id: Int
    let name: String
    let description: String?
    let course: PublicCourse?
    let startDate: String
    let endDate: String?
    let schedule: String?
    let location: ClassLocation?
    let room: ClassRoom?
    let instructor: CourseInstructor?
    let maxStudents: Int?
    let currentStudents: Int
    let learningType: String?
    let status: String
    let registrationDeadline: String?
    let syllabus: [SyllabusItem]?
    let requirements: [String]?
    
    enum CodingKeys: String, CodingKey {
        case id, name, description, course
        case startDate = "start_date"
        case endDate = "end_date"
        case schedule, location, room, instructor
        case maxStudents = "max_students"
        case currentStudents = "current_students"
        case learningType = "learning_type"
        case status
        case registrationDeadline = "registration_deadline"
        case syllabus, requirements
    }
}

struct ClassLocation: Codable {
    let id: Int
    let name: String
    let address: String?
    let city: String?
    let phone: String?
}

struct ClassRoom: Codable {
    let id: Int
    let name: String
    let capacity: Int?
    let roomType: String?
    let facilities: [String]?
    
    enum CodingKeys: String, CodingKey {
        case id, name, capacity
        case roomType = "room_type"
        case facilities
    }
}

struct SyllabusItem: Codable {
    let week: Int?
    let topic: String
    let description: String?
    let duration: String?
}

// MARK: - Locations Models
struct PublicLocationListResponse: Codable {
    let locations: [PublicLocation]
    let totalCount: Int
    let pagination: PaginationInfo?
    
    enum CodingKeys: String, CodingKey {
        case locations
        case totalCount = "total_count"
        case pagination
    }
}

struct PublicLocation: Codable {
    let id: Int
    let name: String
    let address: String?
    let city: String?
    let phone: String?
    let email: String?
    let description: String?
    let facilities: [String]?
    let imageUrl: String?
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, address, city, phone, email, description, facilities
        case imageUrl = "image_url"
        case isActive = "is_active"
    }
}

struct PublicLocationDetailResponse: Codable {
    let location: PublicLocationDetail
}

struct PublicLocationDetail: Codable {
    let id: Int
    let name: String
    let address: String?
    let city: String?
    let phone: String?
    let email: String?
    let description: String?
    let fullDescription: String?
    let facilities: [String]?
    let rooms: [ClassRoom]?
    let imageUrl: String?
    let images: [String]?
    let coordinates: LocationCoordinates?
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, address, city, phone, email, description
        case fullDescription = "full_description"
        case facilities, rooms
        case imageUrl = "image_url"
        case images, coordinates
        case isActive = "is_active"
    }
}

struct LocationCoordinates: Codable {
    let latitude: Double
    let longitude: Double
}

// MARK: - Rooms Models
struct PublicRoomListResponse: Codable {
    let rooms: [PublicRoom]
    let totalCount: Int
    let pagination: PaginationInfo?
    
    enum CodingKeys: String, CodingKey {
        case rooms
        case totalCount = "total_count"
        case pagination
    }
}

struct PublicRoom: Codable {
    let id: Int
    let name: String
    let location: PublicLocation?
    let capacity: Int?
    let roomType: String?
    let facilities: [String]?
    let description: String?
    let isActive: Bool
    
    enum CodingKeys: String, CodingKey {
        case id, name, location, capacity
        case roomType = "room_type"
        case facilities, description
        case isActive = "is_active"
    }
}

// MARK: - Enrollment Models
struct PublicEnrollmentCreate: Codable {
    let firstName: String
    let lastName: String
    let email: String
    let phone: String?
    let dateOfBirth: String?
    let address: String?
    let emergencyContact: EmergencyContact?
    let paymentMethod: String?
    let specialRequirements: String?
    let marketingConsent: Bool?
    
    enum CodingKeys: String, CodingKey {
        case firstName = "first_name"
        case lastName = "last_name"
        case email, phone
        case dateOfBirth = "date_of_birth"
        case address
        case emergencyContact = "emergency_contact"
        case paymentMethod = "payment_method"
        case specialRequirements = "special_requirements"
        case marketingConsent = "marketing_consent"
    }
}

struct EmergencyContact: Codable {
    let name: String
    let relationship: String
    let phone: String
}

struct PublicEnrollmentResponse: Codable {
    let enrollmentId: String
    let course: PublicCourse?
    let classDetail: PublicClass?
    let student: EnrollmentStudent
    let status: String
    let enrolledAt: String
    let paymentStatus: String?
    let confirmationCode: String?
    
    enum CodingKeys: String, CodingKey {
        case enrollmentId = "enrollment_id"
        case course
        case classDetail = "class"
        case student, status
        case enrolledAt = "enrolled_at"
        case paymentStatus = "payment_status"
        case confirmationCode = "confirmation_code"
    }
}

struct EnrollmentStudent: Codable {
    let firstName: String
    let lastName: String
    let email: String
    let phone: String?
    
    enum CodingKeys: String, CodingKey {
        case firstName = "first_name"
        case lastName = "last_name"
        case email, phone
    }
}

// MARK: - Common Models
struct PaginationInfo: Codable {
    let currentPage: Int
    let totalPages: Int
    let perPage: Int
    let totalItems: Int
    let hasNext: Bool
    let hasPrevious: Bool
    
    enum CodingKeys: String, CodingKey {
        case currentPage = "current_page"
        case totalPages = "total_pages"
        case perPage = "per_page"
        case totalItems = "total_items"
        case hasNext = "has_next"
        case hasPrevious = "has_previous"
    }
}

// MARK: - HTTP Validation Error
struct HTTPValidationError: Codable {
    let detail: [ValidationErrorDetail]?
}

struct ValidationErrorDetail: Codable {
    let loc: [String]
    let msg: String
    let type: String
}
