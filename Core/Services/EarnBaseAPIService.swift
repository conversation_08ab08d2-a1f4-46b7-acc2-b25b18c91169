//
//  EarnBaseAPIService.swift
//  mobile-app-template
//
//  Created by Mobile App Template on 22/7/25.
//

import Foundation
import Combine

// MARK: - EarnBase API Service
class EarnBaseAPIService: ObservableObject {
    static let shared = EarnBaseAPIService()
    
    private let apiClient = APIClient.shared
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        // Listen for API configuration changes
        NotificationCenter.default.publisher(for: .apiConfigurationChanged)
            .sink { [weak self] _ in
                self?.handleConfigurationChange()
            }
            .store(in: &cancellables)
    }
    
    private func handleConfigurationChange() {
        // Reinitialize API client or update configuration as needed
        print("API Configuration changed to: \(APIConfiguration.shared.baseURL)")
    }
    
    // MARK: - Core System APIs
    func getAPIInfo() async throws -> APIStatus {
        return try await apiClient.request(
            endpoint: APIEndpoints.Core.apiInfo,
            method: .GET,
            responseType: APIStatus.self,
            requiresAuth: false
        )
    }
    
    func healthCheck() async throws -> ResponseBase {
        return try await apiClient.request(
            endpoint: APIEndpoints.Core.health,
            method: .GET,
            responseType: ResponseBase.self,
            requiresAuth: false
        )
    }
    
    func getModules() async throws -> [String] {
        return try await apiClient.request(
            endpoint: APIEndpoints.Core.modules,
            method: .GET,
            responseType: [String].self,
            requiresAuth: false
        )
    }
    
    // MARK: - Authentication APIs
    func signIn(login: String, password: String, deviceInfo: UnifiedLoginRequest.DeviceInfo? = nil) async throws -> EnhancedTokenResponse {
        let request = UnifiedLoginRequest(
            login: login,
            password: password,
            deviceInfo: deviceInfo
        )
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.signIn,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: EnhancedTokenResponse.self,
            requiresAuth: false
        )
    }
    
    func register(request: UserRegistrationRequest) async throws -> UserRegistrationResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.register,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: UserRegistrationResponse.self,
            requiresAuth: false
        )
    }
    
    func getCurrentUser() async throws -> UserInfoResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.me,
            method: .GET,
            responseType: UserInfoResponse.self,
            requiresAuth: true
        )
    }
    
    func refreshToken(refreshToken: String) async throws -> TokenResponse {
        let parameters = ["refresh_token": refreshToken]
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.refresh,
            method: .POST,
            parameters: parameters,
            responseType: TokenResponse.self,
            requiresAuth: false
        )
    }
    
    func verifyToken() async throws -> ResponseBase {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.verify,
            method: .GET,
            responseType: ResponseBase.self,
            requiresAuth: true
        )
    }
    
    func signOut() async throws -> ResponseBase {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.signOut,
            method: .POST,
            responseType: ResponseBase.self,
            requiresAuth: true
        )
    }
    
    func forgotPassword(email: String) async throws -> ForgotPasswordResponse {
        let request = ForgotPasswordRequest(email: email)
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.forgotPassword,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: ForgotPasswordResponse.self,
            requiresAuth: false
        )
    }
    
    func resetPassword(token: String, newPassword: String) async throws -> ResetPasswordResponse {
        let request = ResetPasswordRequest(token: token, newPassword: newPassword)
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.resetPassword,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: ResetPasswordResponse.self,
            requiresAuth: false
        )
    }
    
    func verifyEmail(token: String) async throws -> EmailVerificationResponse {
        let request = EmailVerificationRequest(token: token)
        
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.verifyEmail,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: EmailVerificationResponse.self,
            requiresAuth: false
        )
    }
    
    func changePassword(request: EnhancedPasswordChangeRequest) async throws -> EnhancedPasswordChangeResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.changePassword,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: EnhancedPasswordChangeResponse.self,
            requiresAuth: true
        )
    }
    
    func registerDevice(request: DeviceRegistrationRequest) async throws -> DeviceRegistrationResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.Auth.deviceRegister,
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: DeviceRegistrationResponse.self,
            requiresAuth: true
        )
    }
    
    // MARK: - Public Events APIs
    func getPublicEvents() async throws -> PublicEventListResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicEvents.list,
            method: .GET,
            responseType: PublicEventListResponse.self,
            requiresAuth: false
        )
    }
    
    func getPublicEventDetail(eventId: Int) async throws -> PublicEventDetailResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicEvents.detail(eventId),
            method: .GET,
            responseType: PublicEventDetailResponse.self,
            requiresAuth: false
        )
    }
    
    func createEventRegistration(eventId: Int, request: PublicEventRegistrationCreate) async throws -> PublicEventRegistrationResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicEvents.registrations(eventId),
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: PublicEventRegistrationResponse.self,
            requiresAuth: false
        )
    }
    
    func getRegistrationsByEmail(email: String) async throws -> PublicEventRegistrationListResponse {
        let parameters = ["email": email]
        
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicEvents.registrationsByEmail,
            method: .GET,
            parameters: parameters,
            responseType: PublicEventRegistrationListResponse.self,
            requiresAuth: false
        )
    }
    
    // MARK: - Public Courses APIs
    func getPublicCourses() async throws -> PublicCourseListResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicCourses.list,
            method: .GET,
            responseType: PublicCourseListResponse.self,
            requiresAuth: false
        )
    }
    
    func searchCourses(query: String = "", page: Int = 1, pageSize: Int = 10) async throws -> PublicCourseListResponse {
        let parameters: [String: Any] = [
            "query": query,
            "page": page,
            "page_size": pageSize
        ]
        
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicCourses.search,
            method: .GET,
            parameters: parameters,
            responseType: PublicCourseListResponse.self,
            requiresAuth: false
        )
    }
    
    func getCourseDetail(courseId: Int) async throws -> PublicCourse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicCourses.detail(courseId),
            method: .GET,
            responseType: PublicCourse.self,
            requiresAuth: false
        )
    }
    
    func createCourseEnrollment(courseId: Int, request: PublicEnrollmentCreate) async throws -> PublicEnrollmentResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicCourses.enrollments(courseId),
            method: .POST,
            parameters: try request.toDictionary(),
            responseType: PublicEnrollmentResponse.self,
            requiresAuth: false
        )
    }
    
    // MARK: - Public Classes APIs
    func getPublicClasses(
        page: Int = 1,
        perPage: Int = 10,
        courseId: Int? = nil,
        startDateFrom: String? = nil,
        learningType: String? = nil
    ) async throws -> PublicClassListResponse {
        var parameters: [String: Any] = [
            "page": page,
            "per_page": perPage
        ]
        
        if let courseId = courseId {
            parameters["course_id"] = courseId
        }
        
        if let startDateFrom = startDateFrom {
            parameters["start_date_from"] = startDateFrom
        }
        
        if let learningType = learningType {
            parameters["learning_type"] = learningType
        }
        
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicClasses.list,
            method: .GET,
            parameters: parameters,
            responseType: PublicClassListResponse.self,
            requiresAuth: false
        )
    }
    
    func getPublicClassDetail(classId: Int) async throws -> PublicClassDetailResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicClasses.detail(classId),
            method: .GET,
            responseType: PublicClassDetailResponse.self,
            requiresAuth: false
        )
    }
    
    // MARK: - Public Locations APIs
    func getPublicLocations(
        page: Int = 1,
        perPage: Int = 10,
        city: String? = nil,
        sortBy: String = "name",
        sortOrder: String = "asc"
    ) async throws -> PublicLocationListResponse {
        var parameters: [String: Any] = [
            "page": page,
            "per_page": perPage,
            "sort_by": sortBy,
            "sort_order": sortOrder
        ]
        
        if let city = city {
            parameters["city"] = city
        }
        
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicLocations.list,
            method: .GET,
            parameters: parameters,
            responseType: PublicLocationListResponse.self,
            requiresAuth: false
        )
    }
    
    func getPublicLocationDetail(locationId: Int) async throws -> PublicLocationDetailResponse {
        return try await apiClient.request(
            endpoint: APIEndpoints.PublicLocations.detail(locationId),
            method: .GET,
            responseType: PublicLocationDetailResponse.self,
            requiresAuth: false
        )
    }
}
